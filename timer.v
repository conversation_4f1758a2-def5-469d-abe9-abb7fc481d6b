`timescale 1ns / 1ps

module timer(
    input wire        rst,
    input wire        clk,
    input wire [31:0] addr,
    input wire        we,
    input wire [31:0] wdata,

    output reg [31:0] rdata
);

    // Timer counter - free running 32-bit counter
    reg [31:0] timer_counter;

    // Prescaler for timer - divide clock by 2^23 (about 12Hz at 100MHz clock)
    // This makes timer increment about 12 times per second at 100MHz
    reg [22:0] prescaler;

    // Timer control register
    reg [31:0] timer_ctrl;

    // Address decode wires for combinational logic
    wire addr_is_20, addr_is_24;

    // Combinational address decode
    assign addr_is_20 = (addr[7:0] == 8'h20);
    assign addr_is_24 = (addr[7:0] == 8'h24);

    // Prescaler logic
    always @(posedge clk) begin
        if(rst) begin
            prescaler <= 23'h0;
        end else begin
            prescaler <= prescaler + 23'd1;
        end
    end

    // Timer counter logic - increment when prescaler overflows
    always @(posedge clk) begin
        if(rst) begin
            timer_counter <= 32'h0;
        end else if(prescaler == 23'h7FFFFF) begin  // Overflow every 2^23 cycles
            timer_counter <= timer_counter + 32'd1;
        end
    end

    // Timer control register logic
    always @(posedge clk) begin
        if(rst) begin
            timer_ctrl <= 32'h0;
        end else if(we && addr_is_24) begin
            timer_ctrl <= wdata;
        end
    end

    // Read data output - combinational for immediate response
    always @(*) begin
        if(addr_is_20) begin
            rdata = timer_counter;
        end else if(addr_is_24) begin
            rdata = timer_ctrl;
        end else begin
            rdata = 32'h0;
        end
    end

endmodule
