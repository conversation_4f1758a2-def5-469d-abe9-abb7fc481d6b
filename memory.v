`timescale 1ns / 1ps

module memory(
    input wire[2:0] ram_rb_op,
    input wire[1:0] ram_wdin_op,
    input wire [31:0] ALUC,
    input wire ram_we,
    input wire [31:0] din,
    input wire clk,
    output reg[31:0] rdo,
    output wire Bus_we,
    output wire[31:0] Bus_addr,
    output reg[31:0] Bus_wdata,
    output reg[3:0] Bus_we_bytes,
    input wire[31:0] Bus_rdata
    );
    assign Bus_addr=ALUC;
    assign Bus_we=ram_we;
    
    parameter WRAM_SB=2'h0;
    parameter WRAM_SH=2'h1;
    parameter WRAM_SW=2'h2;
    parameter RDO_LB=3'h0;
    parameter RDO_LBU=3'h1;
    parameter RDO_LH=3'h2;
    parameter RDO_LHU=3'h3;
    parameter RDO_LW=3'h4;
    // Generate write data and byte enable signals
    always @(*) begin
        if (ram_we) begin
            case (ram_wdin_op)
                WRAM_SW: begin
                    Bus_wdata = din;
                    Bus_we_bytes = 4'b1111;
                end
                WRAM_SB: begin
                    case(ALUC[1:0])
                       2'h0: begin
                           Bus_wdata = {24'h0, din[7:0]};
                           Bus_we_bytes = 4'b0001;
                       end
                       2'h1: begin
                           Bus_wdata = {16'h0, din[7:0], 8'h0};
                           Bus_we_bytes = 4'b0010;
                       end
                       2'h2: begin
                           Bus_wdata = {8'h0, din[7:0], 16'h0};
                           Bus_we_bytes = 4'b0100;
                       end
                       default: begin
                           Bus_wdata = {din[7:0], 24'h0};
                           Bus_we_bytes = 4'b1000;
                       end
                    endcase
                end
                WRAM_SH: begin
                    case(ALUC[1])
                       1'h0: begin
                           Bus_wdata = {16'h0, din[15:0]};
                           Bus_we_bytes = 4'b0011;
                       end
                       default: begin
                           Bus_wdata = {din[15:0], 16'h0};
                           Bus_we_bytes = 4'b1100;
                       end
                    endcase
                end
                default: begin
                    Bus_wdata = din;
                    Bus_we_bytes = 4'b1111;
                end
            endcase
        end else begin
            Bus_wdata = 32'h0;
            Bus_we_bytes = 4'b0000;
        end
    end
    always @(*) begin
        case (ram_rb_op)
            RDO_LW:  rdo = Bus_rdata;
            RDO_LB:begin
                case(ALUC[1:0])
                    2'h0: rdo={{24{Bus_rdata[7]}},Bus_rdata[7:0]};
                    2'h1: rdo={{24{Bus_rdata[15]}},Bus_rdata[15:8]};
                    2'h2: rdo={{24{Bus_rdata[23]}},Bus_rdata[23:16]};
                    default: rdo={{24{Bus_rdata[31]}},Bus_rdata[31:24]};
                endcase
            end  
            RDO_LBU:begin
                case(ALUC[1:0])
                    2'h0: rdo={24'd0,Bus_rdata[7:0]};
                    2'h1: rdo={24'd0,Bus_rdata[15:8]};
                    2'h2: rdo={24'd0,Bus_rdata[23:16]};
                    default: rdo={24'd0,Bus_rdata[31:24]};
                endcase
            end  
            RDO_LH:begin
                case(ALUC[1])
                    1'b0: rdo={{16{Bus_rdata[15]}},Bus_rdata[15:0]};
                    default: rdo={{16{Bus_rdata[31]}},Bus_rdata[31:16]};
                endcase
            end
            RDO_LHU:begin
                case(ALUC[1])
                    1'b0: rdo={16'd0,Bus_rdata[15:0]};
                    default: rdo={16'd0,Bus_rdata[31:16]};
                endcase
            end
            default: rdo = Bus_rdata;
        endcase
    end
endmodule