`timescale 1ns / 1ps
`include "defines.vh"

module miniRV_SoC (
    input  wire         fpga_rst,   // High active
    input  wire         fpga_clk,

    input  wire [23:0]  sw,
    input  wire [ 4:0]  button,
    output wire [ 7:0]  dig_en,
    output wire         DN_A,
    output wire         DN_B,
    output wire         DN_C,
    output wire         DN_D,
    output wire         DN_E,
    output wire         DN_F,
    output wire         DN_G,
    output wire         DN_DP,
    output wire [23:0]  led

`ifdef RUN_TRACE
    ,// Debug Interface
    output wire         debug_wb_have_inst,
    output wire [31:0]  debug_wb_pc,
    output              debug_wb_ena,
    output wire [ 4:0]  debug_wb_reg,
    output wire [31:0]  debug_wb_value      
`endif
);

    wire        pll_lock;
    wire        pll_clk;
    wire        cpu_clk;

    // Interface between CPU and IROM
`ifdef RUN_TRACE
    wire [15:0] inst_addr;
`else
    wire [13:0] inst_addr;
`endif
    wire [31:0] inst;

    // Interface between CPU and Bridge
    wire [31:0] Bus_rdata;
    wire [31:0] Bus_addr;
    wire        Bus_we;
    wire [3:0]  Bus_we_bytes;
    wire [31:0] Bus_wdata;

    // Interface between bridge and DRAM
    // wire         rst_bridge2dram;
    wire         clk_bridge2dram;
    wire [31:0]  addr_bridge2dram;
    wire [31:0]  rdata_dram2bridge;
    wire [3:0]   we_bridge2dram;
    wire [31:0]  wdata_bridge2dram;
    wire        rst_bridge2dig;
    wire        clk_bridge2dig;
    wire [31:0] addr_bridge2dig;
    wire        we_bridge2dig;
    wire [31:0] wdata_bridge2dig;
    
    wire        rst_bridge2led;
    wire        clk_bridge2led;
    wire [31:0] addr_bridge2led;
    wire        we_bridge2led;
    wire [31:0] wdata_bridge2led;
    
    wire        rst_bridge2sw;
    wire        clk_bridge2sw;
    wire [31:0] addr_bridge2sw;
    wire [31:0] rdata_sw2bridge;
    
    wire        rst_bridge2btn;
    wire        clk_bridge2btn;
    wire [31:0] addr_bridge2btn;
    wire [31:0] rdata_btn2bridge;

    wire        rst_bridge2timer;
    wire        clk_bridge2timer;
    wire [31:0] addr_bridge2timer;
    wire        we_bridge2timer;
    wire [31:0] wdata_bridge2timer;
    wire [31:0] rdata_timer2bridge;
    
`ifdef RUN_TRACE
    assign cpu_clk = fpga_clk;
`else
    assign cpu_clk = pll_clk & pll_lock;
    cpuclk Clkgen (
        // .resetn     (!fpga_rst),
        .clk_in1    (fpga_clk),
        .clk_out1   (pll_clk),
        .locked     (pll_lock)
    );
`endif
    
    myCPU Core_cpu (
        .cpu_rst            (fpga_rst),
        .cpu_clk            (cpu_clk),

        // Interface to IROM
        .inst_addr          (inst_addr),
        .inst               (inst),

        // Interface to Bridge
        .Bus_addr           (Bus_addr),
        .Bus_rdata          (Bus_rdata),
        .Bus_we             (Bus_we),
        .Bus_we_bytes       (Bus_we_bytes),
        .Bus_wdata          (Bus_wdata)

`ifdef RUN_TRACE
        ,// Debug Interface
        .debug_wb_have_inst (debug_wb_have_inst),
        .debug_wb_pc        (debug_wb_pc),
        .debug_wb_ena       (debug_wb_ena),
        .debug_wb_reg       (debug_wb_reg),
        .debug_wb_value     (debug_wb_value)
`endif
    );
    
`ifdef RUN_TRACE
    IROM #(.ADDR_BITS(16)) Mem_IROM (
        .a          (inst_addr),
        .spo        (inst)
    );
`else
    IROM #(.ADDR_BITS(14)) Mem_IROM (
        .a          (inst_addr),
        .spo        (inst)
    );
`endif
    
    Bridge Bridge (       
        // Interface to CPU
        .rst_from_cpu       (fpga_rst),
        .clk_from_cpu       (cpu_clk),
        .addr_from_cpu      (Bus_addr),
        .we_from_cpu        (Bus_we),
        .we_bytes_from_cpu  (Bus_we_bytes),
        .wdata_from_cpu     (Bus_wdata),
        .rdata_to_cpu       (Bus_rdata),
        
        // Interface to DRAM
        // .rst_to_dram    (rst_bridge2dram),
        .clk_to_dram        (clk_bridge2dram),
        .addr_to_dram       (addr_bridge2dram),
        .rdata_from_dram    (rdata_dram2bridge),
        .we_to_dram         (we_bridge2dram),
        .wdata_to_dram      (wdata_bridge2dram),
        
        // Interface to 7-seg digital LEDs
        .rst_to_dig         (rst_bridge2dig),
        .clk_to_dig         (clk_bridge2dig),
        .addr_to_dig        (addr_bridge2dig),
        .we_to_dig          (we_bridge2dig),
        .wdata_to_dig       (wdata_bridge2dig),

        // Interface to LEDs
        .rst_to_led         (rst_bridge2led),
        .clk_to_led         (clk_bridge2led),
        .addr_to_led        (addr_bridge2led),
        .we_to_led          (we_bridge2led),
        .wdata_to_led       (wdata_bridge2led),

        // Interface to switches
        .rst_to_sw          (rst_bridge2sw),
        .clk_to_sw          (clk_bridge2sw),
        .addr_to_sw         (addr_bridge2sw),
        .rdata_from_sw      (rdata_sw2bridge),

        // Interface to buttons
        .rst_to_btn         (rst_bridge2btn),
        .clk_to_btn         (clk_bridge2btn),
        .addr_to_btn        (addr_bridge2btn),
        .rdata_from_btn     (rdata_btn2bridge),

        // Interface to timer
        .rst_to_timer       (rst_bridge2timer),
        .clk_to_timer       (clk_bridge2timer),
        .addr_to_timer      (addr_bridge2timer),
        .we_to_timer        (we_bridge2timer),
        .wdata_to_timer     (wdata_bridge2timer),
        .rdata_from_timer   (rdata_timer2bridge)
    );

    DRAM Mem_DRAM (
        .clk        (clk_bridge2dram),
        .a          (addr_bridge2dram[15:2]),
        .spo        (rdata_dram2bridge),
        .we         (we_bridge2dram),
        .d          (wdata_bridge2dram)
    );
    dig U_dig (
        .rst(rst_bridge2dig),
        .clk(clk_bridge2dig),
        .addr(addr_bridge2dig),
        .we(we_bridge2dig),
        .wdata(wdata_bridge2dig),
        .dig_en(dig_en),
        .DN_A(DN_A),
        .DN_B(DN_B),
        .DN_C(DN_C),
        .DN_D(DN_D),
        .DN_E(DN_E),
        .DN_F(DN_F),
        .DN_G(DN_G),
        .DN_DP(DN_DP)
    );
    
    led U_led (
        .rst(rst_bridge2led),
        .clk(clk_bridge2led),
        .addr(addr_bridge2led),
        .we(we_bridge2led),
        .wdata(wdata_bridge2led),
        .led(led)
    );

    sw U_sw (
        .rst(rst_bridge2sw),
        .clk(clk_bridge2sw),
        .addr(addr_bridge2sw),
        .sw(sw),
        .rdata(rdata_sw2bridge)
    );

    btn U_btn (
        .rst(rst_bridge2btn),
        .clk(clk_bridge2btn),
        .addr(addr_bridge2btn),
        .button(button),
        .rdata(rdata_btn2bridge)
    );

    // 如果需要使用简化版本，请取消注释下一行并注释掉当前行
    // timer_ultra_simple U_timer (
    timer U_timer (
        .rst(rst_bridge2timer),
        .clk(clk_bridge2timer),
        .addr(addr_bridge2timer),
        .we(we_bridge2timer),
        .wdata(wdata_bridge2timer),
        .rdata(rdata_timer2bridge)
    );

endmodule